import React, { useEffect, useMemo, useState, useRef, useCallback } from 'react'
import { useContextMenu } from './useContextMenu'
import { ICellRendererParams } from '@ag-grid-community/core'
import styles from './SimpleMarkRenderer.module.scss'
import { Tooltip } from 'antd'
import { useDispatch, useSelector } from 'src/hook'
import { isEmpty } from 'lodash'
import { isCellTag } from 'src/pageTabs/queryPage/resultTabs/resultContentGrid/CellViewerModal'
import { setDoubleClickCell, createCursorResultTab } from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import i18n from 'i18next';

// 🧪 测试版本4：基础版本 + 组件卸载检测
export const SimpleTextRenderer = (params: ICellRendererParams) => {
  // 立即提取值，避免保持对 params 的引用
  const value = params.value
  return <div>{value ?? ''}</div>
}

export const SimpleTextRendererWithContextMenu = (
  params: ICellRendererParams,
) => {
  return SimpleTextRenderer(params)
  // const [wrapper] = useContextMenu(params)
  // return wrapper(SimpleTextRenderer(params))
}
