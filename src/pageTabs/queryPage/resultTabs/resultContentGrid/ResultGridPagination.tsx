import {
  ColDef,
  Column,
  GridReadyEvent,
  IDatasource,
  IGetRowsParams,
  RowEditingStartedEvent,
  RowEditingStoppedEvent,
  CellClickedEvent,
  RowNode,
} from '@ag-grid-community/core'
import type { CellKeyDownEvent, ICellRendererParams } from '@ag-grid-community/core'
import { AgGridReact } from '@ag-grid-community/react'
import { message, Modal, Spin } from 'antd';
import classNames from 'classnames'
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { cloneDeep,isEmpty, isEqual } from 'lodash'
import {
  ActioneType,
  downloadGridCell,
  executeSqlSegment,
  executeSqlStatement,
  explainSqlStatement,
  getCompleteCell,
  makeResultAllExport,
  PlainRowData,
  QueryBase,
  QueryResult,
  ResultColumnInfo,
  ResultModify,
  getSegmentResults,
  QueryParams,
  updateTableCountHint,
  formatAndModifyResult
} from 'src/api'
import { createMatchPattern, performRowMatching } from './utils/matchUtils'
import { CustomHeader, Iconfont, indexColDef, indexColId, exportTaskCreatedNot } from 'src/components'
import CustomHeaderStyles from 'src/components/agFrameworkComponents/CustomHeader.module.scss'
import type { TextEditorProps, IGridContext } from 'src/components'
import { useDispatch, useSelector, useRequest } from 'src/hook'
import { openFlowForm } from 'src/pageTabs/flowPages/flowFormsSlice'
import {
  setPageTotalByQueryKey,
  updateResultTabs,
  setActiveResultTabKey,
  setDoubleClickCell
} from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import Service from 'src/service'
import {
  activePaneInfoSelector,
  pushMonacoValue,
  setTabExecutionStatus, setTabExecutionStatusPercentage,
} from '../../queryTabs/queryTabsSlice'
import { useCopyableChange } from '../useCopyableChange'
import { AddResultExportModal } from './AddResultExportModal'
import { AddSelectedResultExportModal } from './AddSelectedResultExportModal'
import { CellViewerModal, isCellTag } from './CellViewerModal'
import styles from './grid.module.scss'
import {
  defaultColDef,
  GridConfigBase,
  infiniteModeOptions,
} from './gridConfig/GridConfig'
import { ResultToolbar, ToolbarActionType } from './ResultToolbar'
import { RowViewerModal } from './RowViewerModal'
import { getAlteredRowData, getExpandedCurResultData, getAgGridCellDownKeys } from './util'
import { useGridStatus, IGridStatus } from './useGridStatus'
import { ResultAllExport } from './ResultAllExport'
import {
  saveExecuteActiveTabParams
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { useHandleExternalInfo } from 'src/hook/useHandleExternalInfo'
import { FocusedRowData } from './ResultGrid';
import { DesensitizedFieldsModal } from './DesensitizedFieldsModal'
import { handleCopyCell, handleCopyRow, handleCopyAll, handleCopyCellTitle, handleCopyAllTitle, handleCopyWithTitle, handleCopySelectAllWithTitle, handleCopySelectedAllCellsTitles } from 'src/components/agFrameworkComponents/ResultMenuFunc';
import { copyTextToClipboard } from 'src/util';
import { useTranslation } from 'react-i18next';
import { useSearch } from './hooks/useSearch'
import ResultSearchBar from './ResultSearchBar'
import { useSelectionController } from './hooks/useSelectionController'

interface ResultGridProps {
  tabResultKey?: string
  result: QueryResult
  queryKey: string
  type?: 'explain' | 'execute'
  executePercentage?: number
  executeStatusMessage?: string
  rowNum: number // 分页数
}

let prevCanSave = false // 记录上一次可保存状态
export const ResultGridPagination: React.FC<ResultGridProps> = memo((props) => {
  const { t } = useTranslation()
  const { locales } = useSelector((state) => state.login)
  const gridConfig = GridConfigBase(locales);
  const dispatch = useDispatch()
  const [rowViewerResultData, setRowViewerResultData] = useState<any>([])
  const { tabResultKey, result, queryKey, type } = props

  const {
    connectionId,
    dataSourceType,
    columnInfos,
    detailedResultData,
    statement,
    statementObject,
    statementObjectWithQuoteCharacter,
    editable,
    operatingObject,
    operatingObjectWithQuoteCharacter,
    dataExport,
    databaseName,
    refreshable,
    unEditableReason,
    scale,
    permissionResult,
    filteredColumns,
    desensitized,
    doubleCheckType,
    isDesensitizedPlaintext,
    currentParams,
    resultData: initCurrentRusultData,
    tableCountHint = -1,
    pagination={pageNumber: 1, pageSize: props.rowNum || 100, total: null, maxNumber:  null},
    enableGenerateSql,
    maskedPathMap = {},
    cursorInfo
  } = result || {}


  const allHotKeys = useSelector(
    (state) => state.setting.hotKeys,
  )
  // 有脱敏字段并且开启了二次复核
  const isDesensitized = desensitized && ![undefined, null, 'NONE'].includes(doubleCheckType)
  const isExplain = statement.includes('explain')
  const { status, dispatchAction } = useGridStatus()
  const [loading, setLoading] = useState<boolean>(true)
  const [actionType, setActionType] = useState<ActioneType>('ROLLING_RESULT')
  const [visibleCellViewer, setVisibleCellViewer] = useState(false)
  const [visibleRowViewer, setVisibleRowViewer] = useState(false)
  const [visibleCommonExport, setVisibleCommonExport] = useState(false)
  const [visibleSelectedExport, setVisibleSelectedExport] = useState(false)
  const [visibleExportAll, setVisibleExportAll] = useState(false)
  const [visibleDesensitizedFields, setVisibleDesensitizedFields] = useState(false)
  const [curPagination, setCurPagination] = useState<{pageNumber: number; pageSize: number; total: number | null;maxNumber: number | null}>(pagination)
  const [hasNextPage, setHasNextPage] = useState<boolean>(false) // N+1分页判断是否有下一页
  
  // 动态计算 rowNum，优先使用当前分页状态的 pageSize，然后是 props 传入的值，最后默认 100
  const rowNum =  props.rowNum || curPagination?.pageSize || 100
  
  const cachePageResultDataRef = useRef<any[]>([]);  //当前页面数据
  // const cacheAllPageResultDataRef = useRef<{[k:number]: any[]}>({});//存储{page1: [],page2: []} 取值时候方便
  const { theme } = useSelector((state) => state.appearance)
  const { plSql } = useSelector(activePaneInfoSelector) || {}
  const { handleExternalInfo } = useHandleExternalInfo()
  const [resultOperationDates, setResultOperationDates] = useState<any[]>([])  // 动态编辑(增删改)结果集数据
  const preEditRowRef = useRef<any>(null)  // 进入编辑状态时暂存编辑前的行数据, 用于 dirty check
  const [focusedRowData, setFocusedRowData] = useState<FocusedRowData>()
  const [focusedColumn, setFocusedColumn] = useState<Column | null>(null)
  const [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null)
  const notSupportPageResultDataRef = useRef<any[]>([]); // 不支持分页的所有结果集数据
  const cloneResultDataRef = useRef<any[]>([]);    // 结果集克隆数据
  const [canSave, setCanSave] = useState(false)  // 结果集是否可以保存
  const [canDelete, setCanDelete] = useState(false) //  是否可以删除
  const [enabledActions, setEnabledActions] = useState<any[]>([])
  const [selectedNodes, setSelectedNodes] = useState<any[]>([])
  const [visibleRowCount, setVisibleRowCount] = useState(0);
  const [aggridKey, setAggridKey] = useState<number>(0); // 用于标识 ag-grid 的 key -- 用于重新渲染
  const modelUpdatedHandlerRef = useRef<(() => void) | null>(null); // 保存监听器引用以便后续移除
  //结果集排序
  const [sortModel, setSortModel] = useState<any>([])
  //结果集筛选
  const [filterModel, setFilterModel] = useState<any>([])
  // 用于存储后端返回的 filterSql（用于判断是否为过滤模式）
  const [filterSql, setFilterSql] = useState<string | null>(null)

  // 结果集 allow copy
  const [copyableRef, allowCopy] = useCopyableChange()
  const canCopy = permissionResult?.resultSetCopy && allowCopy;
  // //复制单元格权限
  const canCopyCell = result?.permissionResult?.resultCellCopy && allowCopy;

  // 使用自定义选择控制器 hook（分页模式，保持列选择功能）
  const {
    customSelectionControllerRef,
    gridContainerRef,
    contextMenuHandlersRef,
    selectionContext,
    contextHandlersReady,
    updateSelectionContext,
    getCellInfoFromMouseEvent,
    handleCellClicked,
    initializeSelectionController,
    setupDragSelection,
    cleanupSelectionController,
  } = useSelectionController({ isScrollLoading: false })

  useEffect(()=>{
    let initActions = [...enabledActionsMap[status]]
    if(canSave){
      initActions.push('save')
    }
    if(canDelete){
      initActions.push('delete')
    }
    if(focusedRowData && Object.keys(focusedRowData).length){
      initActions.push('view')
    }else {
      initActions = initActions?.filter((i: string)=>i !== 'view')
    }
    setEnabledActions(initActions)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[status, canSave, canDelete, focusedRowData])

  useEffect(()=>{
    return ()=>{
      cachePageResultDataRef.current = [];
      // cacheAllPageResultDataRef.current = [];
      // rowNum更新后 ag-grid 也需要重新渲染
      setAggridKey(prevKey => prevKey + 1)
    }
  },[rowNum])

  useEffect(() => {
    //设置单独结果集scale值 queryKey {}
    const resultTabs = Object.keys(resultTabMap).map(key => {
      if (key === tabResultKey) {
        return {
          key,
          info: {
            ...resultTabMap[key].info,
            pagination: curPagination
          }
        }
      }
      return resultTabMap[key]
    })
    dispatch(updateResultTabs(resultTabs))
  },[curPagination])

  const formatColumn = (columns: ResultColumnInfo[], sortModel: any) => {
    return columns.map((def) => {
      const {
        columnName,
        comment,
        renderType,
        cellEditor,
        desensitization,
        sortable,
        filterable,
        nullable,
        downloadable,
        editable: columnEditable,
        unEditableReason: columnUnEditableReason,
      } = def
      const isDesens = Service.moduleService.isModuleExist('/flow')
        ? desensitization?.status
        : false

      const cellEditorParams: Pick<
        TextEditorProps,
        'cellEditorType' | 'nullable' | 'renderType' | 'downloadable'
      > = {
        cellEditorType: cellEditor,
        nullable,
        renderType,
        downloadable // 单元格是否支持下载
      }

      return {
        minWidth: 80, // 最小宽度
        field: columnName,
        sortable: !['INSERT', 'UPDATE'].includes(status) && sortable,
        filter: !['INSERT', 'UPDATE'].includes(status) && filterable,
        editable: (params: any) => {
          // todo: do not hack
          // 如果是undefined，则消除影响
          let columnEditableFilter =
            columnEditable === undefined ? true : columnEditable;
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
          if (connectionType === 'DB2') {
            columnEditableFilter = false;
          }
          return (
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value))
            )
          )
        },
        context:{
          editable,
          columnEditable,
          columnUnEditableReason,
          unEditableReason,
          renderType
        },
        cellRenderer: 'SimpleTextRendererWithContextMenu',
        cellEditor: 'textEditor',
        cellEditorParams,
        headerComponentFramework: CustomHeader,
        headerComponentParams: {
          headerName: columnName,
          comment,
          isDesens,
          copyable: canCopy,
          sortModel,
          onHeaderLabelClick: (column: Column, mouseEvent: React.MouseEvent) => {
            // 确保是鼠标左键点击
            if (mouseEvent.button === 0) {
              if (customSelectionControllerRef.current) {
                customSelectionControllerRef.current.onHeaderClicked(column, mouseEvent.nativeEvent as MouseEvent);
                updateSelectionContext();
                if (gridApiRef.current?.api) {
                  gridApiRef.current.api.refreshCells();
                }
              }
            }
          },
          // 添加选择上下文相关的方法
          selectionContext: selectionContext,
          handleResInsert: (params: any) => {
            return handleResInsert(params)
          },
          handleResUpdate: (params: any) => {
            return handleResUpdate(params)
          },
          handleResDelete: (params: any) => {
            return handleResDelete(params)
          },
          // 获取选中行数据函数
          getSelectedRowsDataForResultSet: () => {
            return contextMenuHandlersRef.current?.getSelectedRowsDataForResultSet() || []
          },
          // 动态菜单处理函数
          handleCopySelectedColumnTitles: (params: any) => {
            return contextMenuHandlersRef.current?.handleCopySelectedColumnTitles(params) || undefined
          },
          handleCopySelectedColumnData: (params: any) => {
            return contextMenuHandlersRef.current?.handleCopySelectedColumnData(params) || undefined
          },
          handleCopySelectedFullColumnDataWithTitle: (params: any) => {
            return contextMenuHandlersRef.current?.handleCopySelectedFullColumnDataWithTitle(params) || undefined
          },
          // 复制全部处理函数
          handleCopyAll: (context: ICellRendererParams) => {
            handleCopyAll(context, cachePageResultDataRef.current)
          },
          // 清除列选择的方法
          clearColumnSelections: () => {
            if (customSelectionControllerRef.current) {
              customSelectionControllerRef.current.clearAllSelections()
              updateSelectionContext()
              if (gridApiRef.current?.api) {
                gridApiRef.current.api.refreshCells()
              }
            }
          },
          // 传递权限相关的配置
          allowCreateSql: permissionResult?.resultSetSqlGenerator && permissionResult?.supportResultSetSqlGenerator && enableGenerateSql && editable,
          createSqlDisabled: permissionResult?.resultSetSqlGenerator && enableGenerateSql,
          // 传递 api 和 columnApi 引用
          api: () => gridApiRef.current?.api,
          columnApi: () => gridApiRef.current?.columnApi,
        },
        // 监听双击事件，触发不可编辑的tooltip
        onCellDoubleClicked: (params: any) => {
          const { rowIndex, column } = params
          const { colId } = column
          const columnEditableFilter = columnEditable === undefined ? true : columnEditable
          const cellEditable =
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value)),
            )
          if (!cellEditable) {
            dispatch(setDoubleClickCell({ colId, rowIndex }))
          }
        },
        valueSetter: (params: any) => {
          // 新增_initData属性存储被修改字段初始值
          if(!params.data._initData){
            params.data._initData = {}
          }
          if(!params.data._initData[columnName]){
            params.data._initData[columnName] = params.oldValue;
          }
          params.data[columnName] = params.newValue;
          return params.oldValue !== params.newValue;
        },
        valueGetter: (params: any) => {
          return params.data?.[columnName]
        },
        cellClassRules: {
          // 脱敏数据置灰
          'desensitized-cell': (params: any) => {
            if (!isDesens) return false;
            if (!params.node || typeof params.node.id === 'undefined' || !params.colDef || !params.colDef.field) return true;
            
            // 检查是否是搜索匹配项或当前焦点匹配项
            const cellKey = `${params.node.id}_${params.colDef.field}`;
            const isAllMatchesHighlight = searchHighlightRefs.matchedCellsSetRef.current.has(cellKey);
            const isCurrentFocusHighlight = searchHighlightRefs.currentFocusCellKeyRef.current === cellKey;
            
            // 只有当不是高亮状态时才应用脱敏样式
            return !isAllMatchesHighlight && !isCurrentFocusHighlight;
          },
          'all-matches-text-highlight': (params: any) => {
            if (!params.node || typeof params.node.id === 'undefined' || !params.colDef || !params.colDef.field) return false;
            return searchHighlightRefs.matchedCellsSetRef.current.has(`${params.node.id}_${params.colDef.field}`);
          },
          'current-match-focus-highlight': (params: any) => {
            if (!params.node || typeof params.node.id === 'undefined' || !params.colDef || !params.colDef.field) return false;
            return searchHighlightRefs.currentFocusCellKeyRef.current === `${params.node.id}_${params.colDef.field}`;
          },
          // 单元格选中高亮 - 排除行头（序号列）
          'ag-cell-selected-manual': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            // 排除序号列（行头）- 使用 indexColId 进行匹配
            if (params.colDef?.colId === indexColId) return false;

            // 使用短路求值优化性能：先检查全选状态，如果是全选则直接返回 true
            // 这样可以避免在全选模式下执行更复杂的 isCellSelected 检查
            return customSelectionControllerRef.current.isSelectAllActive() ||
                   customSelectionControllerRef.current.isCellSelected(params.node.rowIndex, columnName);
          },
          // 列选中高亮
          'ag-column-selected-custom': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            return customSelectionControllerRef.current.isColumnSelected(columnName);
          },

        },
      }
    })
  }

  const formatOracleCustomColumn = (columns: ResultColumnInfo[] | any,prefix='',sortModel: any) => {
    return columns.map((def: ResultColumnInfo) => {
      const {
        columnName,
        comment,
        renderType,
        cellEditor,
        desensitization,
        sortable,
        filterable,
        nullable,
        downloadable,
        editable: columnEditable,
        childColumnInfos = [],
        unEditableReason: columnUnEditableReason,
      } = def
      const isDesens = Service.moduleService.isModuleExist('/flow')
        ? desensitization?.status
        : false

      const cellEditorParams: Pick<
        TextEditorProps,
        'cellEditorType' | 'nullable' |'renderType' | 'downloadable'
      > = {
        cellEditorType: cellEditor,
        nullable,
        renderType,
        downloadable // 单元格是否支持下载
      }
  
      const curPrefix = prefix ? `${prefix}_${columnName}` : columnName;
      return {
        minWidth: 80, // 最小宽度
        field: curPrefix,
        sortable: !['INSERT', 'UPDATE'].includes(status) && sortable,
        filter: !['INSERT', 'UPDATE'].includes(status) && filterable,
        editable: (params: any) => {
          // todo: do not hack
          // 如果是undefined，则消除影响
          let columnEditableFilter =
            columnEditable === undefined ? true : columnEditable;
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
           if (connectionType === 'DB2') {
            columnEditableFilter = false;
          }
          return (
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value)),
            )
          )
        },
        context:{
          editable,
          columnEditable,
          columnUnEditableReason,
          unEditableReason,
          renderType
        },
        cellRenderer: 'SimpleTextRendererWithContextMenu',
        cellEditor: 'textEditor',
        cellEditorParams,
        headerName: columnName, //组名
        headerComponentFramework: CustomHeader,
        headerComponentParams: {
          headerName: columnName,
          comment,
          isDesens,
          copyable: canCopy,
          sortModel,
          onHeaderLabelClick: (column: Column, mouseEvent: React.MouseEvent) => {
            // 确保是鼠标左键点击
            if (mouseEvent.button === 0) {
              if (customSelectionControllerRef.current) {
                customSelectionControllerRef.current.onHeaderClicked(column, mouseEvent.nativeEvent as MouseEvent);
                updateSelectionContext();
                if (gridApiRef.current?.api) {
                  gridApiRef.current.api.refreshCells();
                }
              }
            }
          },
          // 添加选择上下文相关的方法
          selectionContext: selectionContext,
          // 结果集生成处理函数
          handleResInsert: (params: any) => {
            return handleResInsert(params)
          },
          handleResUpdate: (params: any) => {
            return handleResUpdate(params)
          },
          handleResDelete: (params: any) => {
            return handleResDelete(params)
          },
          // 获取选中行数据函数
          getSelectedRowsDataForResultSet: () => {
            return contextMenuHandlersRef.current?.getSelectedRowsDataForResultSet() || []
          },
          // 动态菜单处理函数
          handleCopySelectedColumnTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnTitles : undefined,
          handleCopySelectedColumnData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnData : undefined,
          handleCopySelectedFullColumnDataWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedFullColumnDataWithTitle : undefined,
          // 复制全部处理函数
          handleCopyAll: (context: ICellRendererParams) => {
            handleCopyAll(context, cachePageResultDataRef.current)
          },
          // 传递权限相关的配置
          allowCreateSql: permissionResult?.resultSetSqlGenerator && permissionResult?.supportResultSetSqlGenerator && enableGenerateSql && editable,
          createSqlDisabled: permissionResult?.resultSetSqlGenerator && enableGenerateSql,
          // 传递 api 和 columnApi 引用
          api: () => gridApiRef.current?.api,
          columnApi: () => gridApiRef.current?.columnApi,
        },
        ...(childColumnInfos?.length ? { colId: columnName } : {}), //oracle自定义时候单元行缺少roleId 只有child有
        ...(childColumnInfos?.length ? { children: formatOracleCustomColumn(childColumnInfos, curPrefix, sortModel) } : {}),
        onCellDoubleClicked: (params: any) => {
          const { rowIndex, column } = params
          const { colId } = column
          const columnEditableFilter = columnEditable === undefined ? true : columnEditable
          const cellEditable =
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value)),
            )
          if (!cellEditable) {
            dispatch(setDoubleClickCell({ colId, rowIndex }))
          }
        },
        valueSetter: (params: any) => {
          // 新增_initData属性存储被修改字段初始值
          if(!params.data._initData){
            params.data._initData = {}
          }
          if(!params.data._initData[columnName]){
            params.data._initData[columnName] = params.oldValue;
          }
          params.data[columnName] = params.newValue;
          return params.oldValue !== params.newValue;
        },
        valueGetter: (params: any) => {
          return params.data?.[columnName]
        },
        cellClassRules: {
          // 脱敏数据置灰 - 仅当不是搜索高亮时应用
          'desensitized-cell': (params: any) => {
            if (!isDesens) return false;
            if (!params.node || typeof params.node.id === 'undefined' || !params.colDef || !params.colDef.field) return true;
            
            // 检查是否是搜索匹配项或当前焦点匹配项
            const cellKey = `${params.node.id}_${params.colDef.field}`;
            const isAllMatchesHighlight = searchHighlightRefs.matchedCellsSetRef.current.has(cellKey);
            const isCurrentFocusHighlight = searchHighlightRefs.currentFocusCellKeyRef.current === cellKey;
            
            // 只有当不是高亮状态时才应用脱敏样式
            return !isAllMatchesHighlight && !isCurrentFocusHighlight;
          },
          // 单元格选中高亮
          'ag-cell-selected-manual': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            
            // 使用短路求值优化性能：先检查全选状态，如果是全选则直接返回 true
            // 这样可以避免在全选模式下执行更复杂的 isCellSelected 检查
            return customSelectionControllerRef.current.isSelectAllActive() ||
                   customSelectionControllerRef.current.isCellSelected(params.node.rowIndex, curPrefix);
          },
          // 列选中高亮
          'ag-column-selected-custom': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            return customSelectionControllerRef.current.isColumnSelected(curPrefix);
          },
          'all-matches-text-highlight': (params: any) => {
            if (!params.node || typeof params.node.id === 'undefined' || !params.colDef || !params.colDef.field) return false;
            return searchHighlightRefs.matchedCellsSetRef.current.has(`${params.node.id}_${params.colDef.field}`);
          },
          'current-match-focus-highlight': (params: any) => {
            if (!params.node || typeof params.node.id === 'undefined' || !params.colDef || !params.colDef.field) return false;
            return searchHighlightRefs.currentFocusCellKeyRef.current === `${params.node.id}_${params.colDef.field}`;
          },
        },
      }
    })
  }

  const currentRusultData: any = dataSourceType && dataSourceType === 'Oracle' ? getExpandedCurResultData(detailedResultData) : initCurrentRusultData

  const { tabInfoMap, paneInfoMap, activeTabKey } = useSelector(
    (state) => state.queryTabs,
  )
  const {
    resultTabMap,
  } = useSelector((state) => state.resultTabs)
  const setPageTotal = useCallback((pageTotal: number) => {
    dispatch(setPageTotalByQueryKey({ queryKey, pageTotal }))
  }, [dispatch, queryKey])
  const { copySetting } = useSelector((state) => state.login.userInfo)
  const paneType = tabInfoMap[queryKey].paneType
  const txMode = paneInfoMap[queryKey].txMode
  const { connectionType, pending } = paneInfoMap?.[activeTabKey] ?? {}

  const columnDefs: ColDef[] = useMemo(() => {
    
    let defaultColumnDefs: ColDef[] = formatColumn(columnInfos, sortModel);

    if (connectionType === 'Oracle') {
      defaultColumnDefs = formatOracleCustomColumn(columnInfos,'', sortModel);
    }
    defaultColumnDefs.unshift({
      ...indexColDef,
      cellRenderer: 'RowIndexFromPropsRendererWithContextMenu',
      pinned: 'left',
      cellRendererParams: {
        newRowIndex: (curPagination?.pageNumber - 1) * (curPagination?.pageSize || rowNum)
      },
    })
    return defaultColumnDefs;
  }, [connectionType, JSON.stringify(columnInfos), editable, curPagination?.pageSize, curPagination?.pageNumber, rowNum, JSON.stringify(sortModel), selectionContext, status])

  /** 各个编辑状态下激活的 toolbarActions */
  const enabledActionsMap: { [key in IGridStatus]: ToolbarActionType[] } = {
    NORMAL: ['refresh', 'add'],
    UPDATE: ['refresh', 'confirm', 'cancel'],
    INSERT: ['refresh', 'confirm'],  // , 'cancel'
    DELETE: ['refresh', 'add'], // , 'delete'
    CLONE: ['refresh', 'confirm'],  // , 'cancel'
  }

  useEffect(() => {
    //hint也得同时处理
    if (rowNum && rowNum !== curPagination?.pageSize) {
      // 查询总行数
      let max = tableCountHint !== -1 ? Math.ceil(tableCountHint / rowNum) : null;
    
      //如果直接切换分页
      if (curPagination?.total) {
        max = Math.ceil(curPagination?.total / rowNum)
      }

      setCurPagination({
        ...curPagination,
        pageSize: rowNum,
        pageNumber: 1,
        maxNumber: max,
        total: curPagination?.total, // 保持已知的总数
      })
      handleRefresh();
    }
  },[rowNum, tableCountHint])

  // 结果页再编辑的参数 columnInfos
  const ColumnInfosEdit = useMemo(
    () =>
      columnInfos.map(({ columnName, cellEditor, defaultValue }) => ({
        columnName,
        columnType: cellEditor,
        defaultValue,
      })),
    [columnInfos],
  )

  const filterNames = useMemo(() => {
    if (!filteredColumns) return []
    return filteredColumns.map(item => item.columnName)
  }, [filteredColumns])

  const getRowDataTemplate = useCallback(() => {
    const newRowData = ColumnInfosEdit.reduce((total: any, colInfo: any) => {
      total[colInfo?.columnName] = colInfo.defaultValue ?? null
      return total
    }, {})
    return newRowData
  }, [ColumnInfosEdit])

    //处理查询到的结果
    const formatExecuteStatementResult = useCallback((data: any) => {
   
      const { executionInfos } = data?.messageData || {};
        let queryResult: QueryResult[] = [];
        executionInfos.forEach((item: any) => {
          queryResult.push(item.response);

          const logMessage = {
            ...(item?.executeLogInfo?.message ?? {}),
            executeSqlFlag: true  // 标记是执行sql产生的日志
          }
          handleExternalInfo({ type: 'LOG', msg: logMessage });

          // 结果集刷新后需要更新表头（比如修改脱敏字段后应更新显示）
          if (actionType === 'REFRESH' && item.response) {
            const newColumns: ColDef[] = connectionType === 'Oracle' ? formatOracleCustomColumn(columnInfos,'',sortModel) : formatColumn(columnInfos, sortModel);

            newColumns.unshift({
              ...indexColDef,
              cellRenderer: 'RowIndexRendererWithContextMenu',
              pinned: 'left'
            });
            item.response.columnInfos && gridApiRef.current?.api.setColumnDefs(newColumns);
          }

          // 捕获 filterSql（用于判断是否为过滤模式）
          if (item.response?.filterSql !== undefined) {
            setFilterSql(item.response.filterSql);
          }
        });
        if (data.executeStatus === 'FAILED') {
          // 日志报错跳转至执行日志tab
          dispatch(setActiveResultTabKey({ queryTabKey: queryKey, resultTabKey: `log/${queryKey}` }));
        }
        // 恢复面板为可执行状态
        dispatch(setTabExecutionStatus({ key: queryKey, pending: false }));
        dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
        return queryResult;
   
  },[])
  /* 执行模块获取的 初始块数据 */
  const isInitializeDataUsed = useRef<boolean>(false)
  /* 获取远程 块数据 */
  const { run: fetchBlockDataOrFromStore } = useRequest(
    async (
      offset: number = 0,
      sortModels: any,
      filterModel: any,
      rowCount: undefined | number =  (curPagination?.pageSize || rowNum) + 1, // N+1分页：请求多一条数据
    ) => {
       //路由切换后 默认展示第一页rowCount不正确
      /* 0 首块数据，第一次获取时使用初始化数据。第二次获取使用远程数据 */
      if (offset === 0 && !isInitializeDataUsed.current) {
        isInitializeDataUsed.current = true
        return Promise.resolve([cloneDeep(result)])
      }

      const payload = {
        connectionId,
        dataSourceType,
        databaseName,
        operatingObject,
        statements: [statement],
        offset,
        rowCount,
        tabKey: queryKey,
        plSql,
        sortModels,
        filterModel,
        autoCommit: txMode === 'auto',
        actionType: actionType,
        // 如果存在 cursorInfo，添加到 payload 中
        ...(cursorInfo ? { cursorInfo } : {})
      }
     
      // 设置执行 pending
      dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));
      if (type === 'explain') {
        const res = explainSqlStatement(isDesensitizedPlaintext ? currentParams : payload)
        // 恢复面板为可执行状态
        dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
        dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
        return res
      } else {
        const res = await executeSqlSegment(isDesensitizedPlaintext ? currentParams : payload)
        const { channelId, groupName, messageId, messageData = [], executeStatus } = res;
      
        //不为pendding即为执行结束
        if (executeStatus !== 'PENDDING' && messageData) {
         return formatExecuteStatementResult(res)
        }
        const params = { channelId, groupName, messageId }
        const result = await recursiveQuery(params);
        return result
      }
    },
    {
      manual: true,
      onSuccess: () => {
        setActionType('ROLLING_RESULT')
      }
    },
  )

  const recursiveQuery = async (params: QueryParams, delay = 100): Promise<QueryResult[]> => {
    try {
      const data = await getSegmentResults(params);
      if (data?.messageData) {
        
      }
      if (data && data.executeStatus === 'RUNNING') {
        // 继续下一次查询，等待 3000 毫秒
        return new Promise((resolve) => {
          setTimeout(async () => {
            // 继续下一次查询，增加延迟从100ms开始，每次增加100ms，但不超过3000ms
            const max = 3000;
            let nextDelay = delay;
            if (delay < max) {
              nextDelay = delay + 100;
            }
            const result = await recursiveQuery(params, nextDelay);
            resolve(result);
          }, delay);
        });
      }
      // 添加一个默认返回值
      return formatExecuteStatementResult(data);
    } catch (error) {
      console.error('轮询出错：', error);
      throw error;
    }
  };

  const getLastRow = useCallback((blockData: unknown[], offset: number) => {
    if (blockData?.length < rowNum) {
      return blockData.length + offset
    }
  }, [])

  const formatFilterModel = (filterModel: IGetRowsParams['filterModel']) => {
    const formattedFilterModel = Object.keys(filterModel)
      .map((key) => {
        const model = filterModel[key]
        if (model.operator) {
          // 双重过滤
          const { condition1, condition2, ...rest } = model
          return [
            key,
            {
              ...rest,
              conditions: [model.condition1, model.condition2],
            },
          ]
        }
        return [key, { conditions: [model] }]
      })
      .reduce(
        (prev, [k, v]) => ({ ...prev, [k]: v }),
        {} as Record<string, any>,
      )
    return formattedFilterModel
  }

  const gridApiRef = useRef<GridReadyEvent | null>(null)

    
  // 搜索功能
  const {
    searchVisible,
    currentSearchOptions,
    currentMatchIndex,
    totalMatches,
    needFullUpdate,
    setSearchVisible,
    handleSearch,
    handleSearchNavigation,
    handleQuickFilter,
    clearAllHighlights,
    retriggerSearchAfterPagination,
    currentSearchTextRef,
    currentSearchOptionsRef,
    needFullUpdateRef,
    setNeedFullUpdate,
    searchHighlightRefs
  } = useSearch({ gridApiRef })




  const handleGridReady = (options: GridReadyEvent): void => {
    gridApiRef.current = options;
    calculateVisibleRowCount();
    
    // 初始化自定义选择控制器（分页模式，保持列选择功能）
    initializeSelectionController(options, gridApiRef);

    const scrollGridWrapper = document.getElementById(`${tabResultKey}gridWrapper`);
    if (scrollGridWrapper) {
      const eventTarget = scrollGridWrapper.querySelector('.ag-body-viewport') as HTMLElement;
      const scrollTarget = scrollGridWrapper.querySelector('.ag-body-horizontal-scroll-viewport') as HTMLElement;
      if (eventTarget && scrollTarget) {
        const handleWheel = (e: WheelEvent) => {
          // 当横向滚动意图明显时 (例如，触控板横向滑动)
          if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
            e.preventDefault();
            e.stopPropagation();
            scrollTarget.scrollLeft += e.deltaX;
          } else if (e.shiftKey) { // 兼容按住 Shift 键用鼠标滚轮横向滚动的操作
            e.preventDefault();
            e.stopPropagation();
            scrollTarget.scrollLeft += e.deltaY;
          }
        };
        eventTarget.addEventListener('wheel', handleWheel, { passive: false });
        options.api.addEventListener('gridDestroyed', () => {
          eventTarget.removeEventListener('wheel', handleWheel);
        });
      }
    }
  }

  const calculateVisibleRowCount = () => {
    const containerHeight = document.getElementById(`${tabResultKey}gridWrapper`)?.clientHeight || 400; // 获取容器高度
    const visibleCount = Math.floor((containerHeight - 32) / 28); // 计算可展示行数
    setVisibleRowCount(visibleCount);
  };

  // 监听窗口大小变化以重新计算可视行数
  useEffect(() => {
    const handleResize = () => {
      calculateVisibleRowCount();
    };

    window.addEventListener('resize', handleResize, { passive: true });
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const curCallbackRef = useRef<(rowsThisBlock: any[], lastRow?: number | undefined)=>void>(()=>{})
  
  const datasource: IDatasource = {
    getRows: async(params) => {
      let startRow = params.startRow;
      let endRow = params.endRow;
      //路由切换调用
      if (curPagination?.pageNumber !== 1) {
        startRow = (curPagination?.pageNumber -1) * curPagination?.pageSize;
        endRow = startRow + curPagination?.pageSize;
      }

      // 确保使用当前保存的过滤和排序状态
      const finalParams = {
        ...params,
        startRow,
        endRow,
        page: curPagination?.pageNumber,
        // 优先使用当前组件状态中的过滤和排序条件
        sortModel: sortModel || params.sortModel || [],
        filterModel: filterModel || params.filterModel || {}
      };
      commonGetRows(finalParams)
    },
  }

  const resetDataStatus = useCallback(() => {
    setCanSave(false)
    setCanDelete(false)
    setResultOperationDates([])
    setSelectedNodes([])
    cachePageResultDataRef.current = []
    notSupportPageResultDataRef.current = [];
    // cacheAllPageResultDataRef.current = {};
    // 重置后 ag-grid 也需要重新渲染
    setAggridKey(prevKey => prevKey + 1)
    setFocusedRowData({})
    setFocusedColumn(null)
    setFocusedRowIndex(null)
    // 注意：这里不重置 curPagination.total，保持已知的总数
  }, [setCanSave, setCanDelete, setResultOperationDates, setSelectedNodes, setAggridKey, setFocusedRowData, setFocusedColumn, setFocusedRowIndex])

  // 辅助函数：恢复过滤状态并更新列头显示
  const restoreFilterStateAndUpdateHeaders = useCallback((filterModelToRestore: any) => {
    if (!gridApiRef.current?.api || !filterModelToRestore || Object.keys(filterModelToRestore).length === 0) {
      return;
    }

    const api = gridApiRef.current.api;

    // 恢复过滤状态
    if (api.setFilterModel)
      api.setFilterModel(filterModelToRestore);
  }, []);

  // 点击刷新按钮
  const handleRefresh = useCallback((preserveFilters = false) => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    setActionType('REFRESH')
    dispatchAction('reset')

    // 保存当前的过滤和排序状态
    let savedFilterModel: any = null
    let savedSortModel: any = null
    let savedFilterSql: string | null = null

    if (preserveFilters) {
      // 从 AG-Grid 获取当前的过滤和排序状态
      savedFilterModel = api.getFilterModel()
      savedSortModel = gridApiRef.current?.columnApi?.getColumnState()?.filter((col: any) => col.sort)?.map((col: any) => ({ colId: col.colId, sort: col.sort })) || []
      savedFilterSql = filterSql
    } else {
      // 重置筛选/排序，清空 cache
      api.setFilterModel(null)
      gridApiRef.current?.columnApi?.applyColumnState({ defaultState: { sort: null } })
      setFilterSql(null) // 重置 filterSql
      setSortModel([])
      setFilterModel([])
    }

    api.ensureIndexVisible(0)
    api.setRowCount(0)
    api.purgeInfiniteCache()
    api.deselectAll()
    // 清除自定义选择
    if (customSelectionControllerRef.current) {
      customSelectionControllerRef.current.clearAllSelections()
      // 触发 cellClassRules 重新评估以清除高亮
      api.refreshCells();
    }
    resetDataStatus()
    cloneResultDataRef.current = []

    // 如果需要保持过滤条件，在清空缓存后恢复状态
    if (preserveFilters) {
      // 恢复组件状态（优先恢复组件状态）
      setSortModel(savedSortModel || [])
      setFilterModel(savedFilterModel || [])
      setFilterSql(savedFilterSql)

      // 延迟恢复 AG-Grid 的过滤和排序状态，确保在数据加载后执行
      setTimeout(() => {
        // 恢复过滤状态并更新列头显示
        restoreFilterStateAndUpdateHeaders(savedFilterModel);

        // 恢复排序状态
        if (savedSortModel && savedSortModel.length > 0) {
          gridApiRef.current?.columnApi?.applyColumnState({
            state: savedSortModel.map((sort: any) => ({ colId: sort.colId, sort: sort.sort }))
          });
        }
      }, 200);
    }
    setLoading(true)
  }, [filterSql, setActionType, dispatchAction, setFilterSql, setSortModel, setFilterModel, customSelectionControllerRef, resetDataStatus, restoreFilterStateAndUpdateHeaders, setLoading])

  // 点击添加按钮
  const handleAddRow = () => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    dispatchAction('startInsert')
    const rowCount = api.getInfiniteRowCount()
    if (typeof rowCount === 'undefined') return
    api.setRowCount(rowCount + 1)
    const initRow = getRowDataTemplate()
    const insertkey = new Date().getTime()
    const row = {
      ...initRow,
      __isNewRow:()=>{
        return insertkey
      }
    }
    const data = [row].concat(cachePageResultDataRef?.current)
    const curCallbackRefCallback = curCallbackRef.current
    curCallbackRefCallback(data)
    cachePageResultDataRef.current = data
    api.startEditingCell({
      rowIndex: 0,
      colKey: columnDefs[1].field!,
    })
  }

  const handleDeleteRowConfirm = () => {
    Modal.confirm({
      title: t('sdo_confirm_delete_selected_row'),
      onOk: () => handleDeleteRow(),
      centered: true,
      okButtonProps: { danger: true },
      okText: t('common.btn.confirm'),
      cancelText: t('common.btn.cancel'),
    })
  }

  // 点击删除按钮
  const handleDeleteRow = () => {
    if (!gridApiRef.current) return
    const selectedNodesData = selectedNodes?.map(({data})=>data)
    const selectedRowIndex = selectedNodes?.map(({rowIndex})=>rowIndex)
    // 删除重置选中值
    selectedNodesData?.forEach((item: any)=>{
      if(isEqual(item, focusedRowData)){
        setFocusedRowData({})
        setFocusedColumn(null)
        setFocusedRowIndex(null)
      }
    })
    let deleteDataArray:any[] = []
    // 判断是否有克隆行在删除行中
    const cloneHasDelCols: boolean = cloneResultDataRef.current?.filter((res: any) => {
      return selectedRowIndex?.includes(res.rowIndex)
    }).length > 0
    // 处理克隆行
    if (cloneHasDelCols) {
      let indexList: number[] = []
      cloneResultDataRef.current = cloneResultDataRef.current?.filter((res: any) => {
        const has = selectedRowIndex?.includes(res.rowIndex)
        if (has) {
          indexList.push(res.rowIndex)
        }
        return !has
      })
      deleteDataArray = selectedNodes?.filter(({ rowIndex }) => !indexList.includes(rowIndex))?.map(({ data }) => ({
        resultOldData: data,
        resultOperating: 'DELETE'
      }))
      setResultOperationDates(s => [...s.filter((item: any) => !item.hasOwnProperty('rowIndex')), ...cloneResultDataRef.current, ...deleteDataArray])
    }
    // 正常删除逻辑
    else {
      deleteDataArray = selectedNodes?.map(({ data }) => ({
        resultOldData: data,
        resultOperating: 'DELETE'
      }))
      setResultOperationDates(s => [...s, ...deleteDataArray])
    }
    // 通过索引过滤,内容过滤没有唯一的key会有问题
    cachePageResultDataRef.current = cachePageResultDataRef?.current?.filter((_: any, index: number) => {
        const has = selectedRowIndex?.includes(index)
        return !has
      })
   
    const curCallbackRefCallback = curCallbackRef.current
    curCallbackRefCallback(cachePageResultDataRef.current,cachePageResultDataRef.current.length)
    setCanSave(true)
    dispatchAction('reset')
  }

  // 点击确定按钮
  const handleConfirmModify = () => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    // 保持修改后的状态退出编辑
    api.stopEditing()
  }

  // 点击取消按钮(插入和克隆不支持取消,因为涉及到多步操作有交叉事件不好处理,要取消可以用删除)
  const handleCancelModify = useCallback(() => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    // 要取消编辑（即不接受更改）
    api.stopEditing(true)
    dispatchAction('reset')
  }, [dispatchAction])

  useEffect(() => {
    const allEditable = selectedNodes?.length > 0 && selectedNodes?.every((n: any) => [true, 'true'].includes(n?.data?.editable))
    if (allEditable) {
      dispatchAction('startDelete');
      setCanDelete(true);
    } else if (status === 'DELETE') {
      dispatchAction('reset');
      setCanDelete(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedNodes, status]);

  // 放大缩小页面
  const handleScaleView = (num: number) => {
    //设置单独结果集scale值 queryKey {}
    const resultTabs = Object.keys(resultTabMap).map(key => {
      if (key === tabResultKey) {
        return {
          key,
          info: {
            ...resultTabMap[key].info,
            scale: num
          }
        }
      }
      return resultTabMap[key]
    })
    dispatch(updateResultTabs(resultTabs))
  }

  // 单元格聚焦
  const handleCellFocused = (event: any) => {
    if (event?.rowIndex != null) {
      setFocusedRowIndex(event?.rowIndex)
      const rowNode = event?.api?.getDisplayedRowAtIndex(event?.rowIndex)
      setFocusedRowData(rowNode?.data)
      setFocusedColumn(event?.column)
    }
  }

  //结果集包含二进制 则不允许修改
  const isColumnContainBlobField = (curColumns: any) => {
    const binaryColumns = curColumns.filter(({ cellEditorParams }: any) => {
      const { renderType } = cellEditorParams || {};
      return renderType === 'binary';
    });
    if (binaryColumns?.length) {
      return true
    }
    return false
  }

  // 进入编辑状态
  const handleStartRowEditing = (event: RowEditingStartedEvent) => {
    // 检查是否有编辑权限，如果没有编辑权限，立即停止编辑
    if (!editable) {
      // 立即停止编辑，不进入编辑状态
      event.api.stopEditing(true);
      return;
    }

    //包含二进制 增加提示
    const curColumns = event.api.getColumnDefs();
    if (curColumns && isColumnContainBlobField(curColumns)) {
      message.warning({ top: 100, content: t('sdo_binary_disallowed_edit') })
    }
    preEditRowRef.current = { ...event.data }
    //进入编辑状态时设置是否点击确认按钮为false
    dispatchAction('startUpdate')
    // 编辑中不可以保存, 先记录编辑前可保存状态方便恢复
    prevCanSave = canSave
    setCanSave(false)
  }

  // 退出编辑状态
  const handleStopRowEditing = async (params: RowEditingStoppedEvent & { edited_markup?: number }) => {
    const { data, rowIndex } = params
    //获取或增加唯一标识符
    const edited_markup = params?.data?.edited_markup || (new Date()).getTime();
    const dataCopy = cloneDeep(data)
    const initData = {...dataCopy?._initData}
    delete dataCopy._initData
    const oldData = {...dataCopy, ...initData}
    const newData = {...dataCopy}
    let resultNewData = getAlteredRowData(oldData, newData)
    const isRowDirty = Object.keys(resultNewData).length;
    // 判断是否是在新插入的行上进行的操作
    const isNewRowUpdate = oldData?.__isNewRow?.() || false
    resultNewData = isNewRowUpdate ? { ...resultNewData, __isNewRow: oldData?.__isNewRow } : { ...resultNewData }
    let resultOperationDates: any[] = [{ resultOldData: oldData, resultNewData, resultOperating: 'UPDATE', edited_markup }]
    // 修改尚未保存的克隆行则不需要再次插入update操作
    if (cloneResultDataRef.current?.length > 0 && status !== 'INSERT') {
      cloneResultDataRef.current = cloneResultDataRef.current.map((iData:any)=>{
        if(iData.rowIndex === rowIndex){
          resultOperationDates = []
          return {
            ...iData,
            resultNewData: data
          }
        }
        else return iData
      })
    }
    if (status === 'INSERT') {
      resultOperationDates = [{ resultNewData, resultOperating: 'INSERT' }]
      if (cloneResultDataRef.current) {
        cloneResultDataRef.current = cloneResultDataRef.current?.map((res: any) => ({
          ...res,
          rowIndex: res.rowIndex + 1
        }))
      }
    } else if (status === 'CLONE') {
      resultOperationDates = []
    } else if (status === 'UPDATE' && !isRowDirty) {
      dispatchAction('reset')
    }
    // 修改克隆行时做替换，不增加update语句
    if(resultOperationDates.length === 0) {
      setResultOperationDates(s=>[...s.filter((item:any)=>!item.hasOwnProperty('rowIndex')), ...cloneResultDataRef.current])
      setCanSave(true)
    }
    else if (isNewRowUpdate && status === 'UPDATE') {
      // 插入行被update时替换原有数据，不增加update语句
      setResultOperationDates(s => {
        const newDatas = s.map((item: any) => {
          const { resultNewData } = item
          if (resultNewData?.__isNewRow?.() === isNewRowUpdate) {
            return { ...item, resultNewData: resultOperationDates[0].resultNewData }
          }
          else return item
        })
        return newDatas
      })
    }
    // 更新操作且有值才保存
    else if(isRowDirty || (resultOperationDates[0]?.resultOperating !== 'UPDATE' && !isRowDirty)){
      
      //原逻辑直接push导致每编辑一次就会增加一条数据，增加标识，防止重复添加
      setResultOperationDates(prevRes => {
        const index = prevRes.findIndex(item => (item?.edited_markup === edited_markup && item?.resultOperating === resultOperationDates[0]?.resultOperating));
        if (index !== -1) {
          // 如果找到相同的 id，覆盖
          return prevRes.map((item, i) => 
            i === index ? { ...item, ...resultOperationDates[0] } : item
          );
        } else {
          // 如果没有找到，新增
          return [...prevRes, ...resultOperationDates];
        }
      });
      setCanSave(true)
    }
    
    // 恢复编辑前可保存的状态
    if(prevCanSave && !canSave){
      setCanSave(true)
    }
    //当前页码数据
    const curCallbackRefCallback = curCallbackRef.current
    //只要是编辑过的行，增加唯一标识符
    if (rowIndex !== null && rowIndex !== undefined) {
      cachePageResultDataRef.current[rowIndex] = {...data, edited_markup}
    }
    curCallbackRefCallback(cachePageResultDataRef.current)
    dispatchAction('reset')
  }

  const getResultSetData = useCallback(() => {
    if (!contextMenuHandlersRef.current) {
      return [];
    }

    let result = contextMenuHandlersRef.current.getSelectedRowsDataForResultSet() || [];
    return result;
  }, [contextMenuHandlersRef]);

  // 结果集生成功能
  const handleResOpe = async(opeDatas:any[])=>{
    const { api } = gridApiRef.current || {};
    if(!api){
      return
    }
    const params: any = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: opeDatas,
    }
    const statements = await formatAndModifyResult(params)
    const tailText = statements?.reduce((pre: string, cur: string, index: number) => {
      if (index < statements.length - 1) return pre + (cur || '') + '\n'
      else return pre + (cur || '')
    }, '')
    copyTextToClipboard(tailText);
    dispatch(
      pushMonacoValue({
        key: activeTabKey,
        tailText:'\n' + tailText,
      }),
    )
  }

  // 保存
  const handleSave = async() => {
    const { api } = gridApiRef.current || {};
    if(!api){
      return
    }
    let isCloneSuccess = false
    const params: any = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: resultOperationDates.map((item: any) => ({
        ...item,
        rowIndex: undefined,
        resultNewData: {
          ...item.resultNewData,
          __isNewRow: undefined
        },
        resultOldData: {
          ...item.resultOldData,
          __isNewRow: undefined
        }
      })),
    }
    // 没有修改直接返回
    if(!resultOperationDates?.length){
      setCanSave(false)
      return
    }
    setLoading(true)
    try {
      const statements = await formatAndModifyResult(params)
      const executeParams = {
        connectionId,
        dataSourceType,
        operatingObject,
        databaseName,
        statements: statements || [],
        tabKey: queryKey,
        autoCommit: txMode === 'auto',
        actionType: 'UPDATE' as const,
      }
      // 设置执行 pending
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));
      dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
      dispatch(saveExecuteActiveTabParams(executeParams))
      const executeResults = await executeSqlStatement(executeParams)
      executeResults?.executionInfos?.forEach((item) => {
        const { response } = item
        if (response?.executeError) {
          message.error(response.executeError.message)
          //若执行出错并且为clone模式 保持当前编辑状态
          if (status === 'CLONE' && focusedRowIndex) {
            isCloneSuccess = true
            api?.setFocusedCell(focusedRowIndex, columnDefs[1].field!)
            api?.startEditingCell({
              rowIndex: focusedRowIndex,
              colKey: columnDefs[1].field!,
            })
            return
          }
        }

        handleExternalInfo({ type: 'LOG', msg: item?.executeLogInfo?.message })
      })
    } catch {
    } finally {
      // 重置数据状态
      setLoading(false)
      resetDataStatus()
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
      //若当前为克隆模式 则失败后不执行刷新和结束编辑
      if (!isCloneSuccess) {
        // 重置用于行详情展示的数据
        setRowViewerResultData([])
        // 结果集修改之后的查询 actionType 为 'QUERY_AFTER_UPDATE'
        setActionType('QUERY_AFTER_UPDATE')
        api?.purgeInfiniteCache()
        dispatchAction('reset')
        setResultOperationDates([])
        cloneResultDataRef.current = []
        return
      }
      cloneResultDataRef.current = []
    }
  }

  // 克隆单行/多行
  const handleCloneRow = useCallback(async (params: ICellRendererParams) => {
    const selectedRows = params.api.getSelectedRows() ?? [];
    const formatData = selectedRows.map((item: any) => Object.fromEntries(
      Object.entries(item).map(([k, v]) => [k, v === null ? '' : String(v)]),
    ))

    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    const rowCount = api.getInfiniteRowCount()
    if (typeof rowCount === 'undefined') return

    dispatchAction('startClone')
    const successCallback = curCallbackRef.current
    params.api?.deselectAll()
    api.setRowCount(rowCount + selectedRows.length)
    const newData = formatData.concat(cachePageResultDataRef.current)
    successCallback(newData)
    // 记录克隆数据
    const formatResults = formatData.map((iData: any) => ({
      resultNewData: iData,
      resultOperating: 'INSERT'
    }))
    cloneResultDataRef.current = formatResults.concat(cloneResultDataRef.current).map((res: any, index: number) => ({
      // 记录rowIndex，后续用户若在保存前update克隆行的数据，则不再新增update数据
      ...res,
      rowIndex: res?.rowIndex ? (res?.rowIndex + selectedRows.length) : index // 定位行号
    }))
    cachePageResultDataRef.current = newData
    api.startEditingCell({
      rowIndex: 0,
      colKey: columnDefs[1].field!,
    })

  }, [])

  const handlePasteRow = useCallback(async (params: ICellRendererParams) => {
    const { colDef, rowIndex, data } = params
    const { field, editable } = colDef as any
    if (!gridApiRef.current) return
    dispatchAction('startPaste')
    let newData = cachePageResultDataRef.current
    let newRow: any[] = []
    let resultOperationDates: any[] = []
    const text = await navigator.clipboard.readText()
    // 是否含有制表符
    let hasTab: boolean = /\t/.test(text)
    if (hasTab) {
      let rows: any[] = text.split('\r\n').map((row) => row.split('\t'))
      rows.pop()
      const end: number = (newData.length - rowIndex > rows.length) ? (rowIndex + rows.length) : newData.length
      const sliceNum = end - rowIndex
      let newArray = newData.slice(rowIndex, end);
      // 从首行序号点入
      if (!editable) {
        newArray.map((item: any, idx: number) => {
          let i: any = {}
          Object.keys(item).map((key: string, index: number) => {
            i[key] = rows?.[idx]?.[index] || ''
          })
          newRow.push(i)
          resultOperationDates.push({
            resultOperating: "UPDATE",
            resultOldData: {
              ...item
            },
            resultNewData: {
              ...i
            }
          })
        })
      }
      // 从某列具体字段点入
      else {
        newArray.map((item: any, idx: number) => {
          let i: any = {}
          if (idx === 0) {
            let editFlag: boolean = false
            let findIndex: number = 0
            Object.keys(item).map((key: string, index: number) => {
              if (key === field) {
                editFlag = true
                findIndex = index
              }
              if (editFlag) {
                i[key] = rows?.[0]?.[index - findIndex]
              }
              else {
                i[key] = item?.[key]
              }
            })
          }
          else {
            Object.keys(item).map((key: string, index: number) => {
              i[key] = rows?.[idx]?.[index] || ''
            })
          }
          newRow.push(i)
          resultOperationDates.push({
            resultOperating: "UPDATE",
            resultOldData: {
              ...item
            },
            resultNewData: {
              ...i
            }
          })
        })
      }
      newData.splice(rowIndex, sliceNum, ...newRow)
    }
    else if (editable) {
      const newRowData = { ...data, [field]: text }
      newRow.push(newRowData)
      resultOperationDates.push({
        resultOperating: "UPDATE",
        resultOldData: {
          ...data
        },
        resultNewData: {
          ...newRowData
        }
      })
      newData.splice(rowIndex, 1, ...newRow)
    }
    else { return }
    setResultOperationDates(s => [...s, ...resultOperationDates])
    const successCallback = curCallbackRef.current
    successCallback(newData)
    cachePageResultDataRef.current = newData
    setCanSave(true)
  }, [])

  // 结果集生成insert
  const handleResInsert = useCallback(async (params: ICellRendererParams) => {
    const selectedRows = getResultSetData();
    const formatSelectDatas = selectedRows?.map((data) => {
      const formatData = Object.fromEntries(
        Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
      )
      return {
        resultNewData: formatData,
        resultOperating: "INSERTSQL"
      }
    })
    handleResOpe([...formatSelectDatas])
  }, [getResultSetData, handleResOpe])

  // 结果集生成update
  const handleResUpdate = useCallback(async (params: ICellRendererParams) => {
    const { colDef, data, value, api, columnApi } = params
    let formatSelectDatas: any[] = []
    const selectedRows = getResultSetData();
    if (selectedRows?.length > 1) {
      const columnDefs = columnApi.getAllDisplayedColumns();
      const columns: any[] = columnDefs.slice(1)
      // 判断是否每个列都可编辑
      const editable: boolean = !columns.find((col: any) =>!col.colDef?.editable?.())
      formatSelectDatas = selectedRows?.map((data) => {
        const formatData = Object.fromEntries(
          Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
        )
        return {
          resultNewData: editable ? formatData : null,
          resultOldData: formatData,
          resultOperating: "UPDATESQL"
        }
      })
    }
    else {
      const { field, editable } = colDef as any
      const formatData = Object.fromEntries(
        Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
      )
      const newData = editable ? { [field]: value } : null
      formatSelectDatas.push({
        resultNewData: field ? newData : formatData,
        resultOldData: formatData,
        resultOperating: "UPDATESQL"
      })
    }
    handleResOpe([...formatSelectDatas])
  }, [getResultSetData, handleResOpe])

  // 结果集生成delete
  const handleResDelete = useCallback(async (params: ICellRendererParams) => {
    const selectedRows = getResultSetData();
    const formatSelectDatas = selectedRows?.map((data) => {
      const formatData = Object.fromEntries(
        Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
      )
      return {
        resultOldData: formatData,
        resultOperating: "DELETESQL"
      }
    })
    handleResOpe([...formatSelectDatas])
  }, [getResultSetData, handleResOpe])

  const updateFocusedCell = async (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => {
    const modifyParams: ResultModify = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: [
        { resultOldData: oldData, resultNewData: newData, resultOperating: 'UPDATE' },
      ],
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
    }

    // 根据执行的操作生成语句, 执行语句并刷新
    setLoading(true)
    try {
      const statements = await formatAndModifyResult(modifyParams)
      const executeParams = {
        connectionId,
        dataSourceType,
        operatingObject,
        databaseName,
        statements: statements || [],
        tabKey: queryKey,
        autoCommit: txMode === 'auto',
        actionType: 'UPDATE' as const,
      }
      // 设置执行 pending
      dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
      dispatch(saveExecuteActiveTabParams(executeParams))
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));
      const executeResults = await executeSqlStatement(executeParams)
      const { executionInfos } = executeResults
      executionInfos.forEach((item) => {
        const { response, executeLogInfo } = item
        if (response?.executeError) message.error(response.executeError.message)
        handleExternalInfo({ type: 'LOG', msg: executeLogInfo?.message })
      })
    } catch {
    } finally {
      setLoading(false)
      resetDataStatus()
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
      dispatchAction('reset')
      setActionType('QUERY_AFTER_UPDATE')
      gridApiRef.current?.api.purgeInfiniteCache()
    }
  }

  const RESULT_HOT_KEYS_MAPPING: any = {
    Copy: (e: any) => copySingleRow(e),
    SelectAll: (e: any) => {
      // 使用 CustomSelectionController 的全选方法
      if (customSelectionControllerRef.current) {
        customSelectionControllerRef.current.selectAllRows();
      }
    },
    JumpToFirstLastPage: (e: any) => moveToLastRowOfNextPage(e, 'next'),
    JumpToFirstRowPage: (e: any) => moveToLastRowOfNextPage(e, 'up'),
    QuicklyJumpToFirstRow: (e: any) => moveToFirstOrLastRowAndColumn(e, 'home'),
    QuicklyJumpToLastRow: (e: any) => moveToFirstOrLastRowAndColumn(e, 'end'),
    QuicklyNavigateFirstColumn:  (e: any) => moveToFirstOrLastColumn(e, 'left'),
    QuicklyNavigateLastColumn:  (e: any) => moveToFirstOrLastColumn(e, 'right'),
   }

  // 统一的键盘事件处理器
  const handleUnifiedKeyDown = useCallback((event: KeyboardEvent) => {
    // 处理 Ctrl+S 阻止默认行为
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 's') {
      event.preventDefault(); 
      return
    }
    
    // 处理 Ctrl+C 复制逻辑
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c') {
      // 检查是否有输入框或编辑区域获得焦点
      const activeElement = document.activeElement as HTMLElement
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true' ||
        activeElement.classList.contains('ag-cell-edit-input')
      )
      
      if (isInputFocused) {
        return
      }
      
      // 阻止默认行为，执行自定义复制逻辑
      event.preventDefault()
      
      // 检查复制权限
      if (!canCopy) {
        message.warning(t('sdo_user_no_copy_permission'))
        return
      }
      
      // 调用统一的复制方法
      if (contextMenuHandlersRef.current) {
        contextMenuHandlersRef.current.handleCopySelection()
      }
    }
  }, [contextMenuHandlersRef, canCopy, t])

  useEffect(()=>{
    const gridWrapper = document.getElementById(`${tabResultKey}gridWrapper`)
    
    if (gridWrapper) {
      // 确保元素可以接收键盘事件
      if (!gridWrapper.hasAttribute('tabindex')) {
        gridWrapper.setAttribute('tabindex', '-1')
      }
      
      // 添加统一的键盘事件监听器
      gridWrapper.addEventListener('keydown', handleUnifiedKeyDown)

      return () => {
        gridWrapper.removeEventListener('keydown', handleUnifiedKeyDown)
      }
    }
  },[handleUnifiedKeyDown, tabResultKey])


  const onCellKeyDown = useCallback((e: CellKeyDownEvent) => {
    if (!e.event) {
      return;
    }
    const keyString = getAgGridCellDownKeys(e);
    
    if (keyString === 'ctrl+c') {
      // 获取正在编辑的单元格
      const editingCells = e.api.getEditingCells();
      // 如果存在正在编辑的单元格，且当前的操作为ctrl+c复制，则不进行任何操作，维持默认行为
      if (editingCells?.length > 0) {
        return
      }
    }
    e.event.preventDefault();
    const keys = Object.keys(RESULT_HOT_KEYS_MAPPING);
    const action =  Object.entries(allHotKeys).find(([key, value]) =>{
      if (value?.toLowerCase() === 'ctrl+delete' ) {
        e.api.stopEditing(); // 停止编辑
       }
      if (value?.toLowerCase() === keyString && keys.includes(key)) return true;
      return false;
    })?.[0];
  
    if (action && Object.keys(RESULT_HOT_KEYS_MAPPING).includes(action)) {
      RESULT_HOT_KEYS_MAPPING[action](e);
    }
  }, [RESULT_HOT_KEYS_MAPPING, allHotKeys]);
  
  const copySingleRow = (e: CellKeyDownEvent) => {
    if (canCopy) {
      handleCopyRow(e as any);
    } else {
      message.warning(t('sdo_user_no_copy_permission'));
    }
  }


  const moveToFirstOrLastRowAndColumn = (event: CellKeyDownEvent, type: 'home' | 'end') => {
    let rowNode =  event?.api?.getRowNode('0');
    const allColumns = event?.columnApi?.getAllDisplayedColumns();
    let rowIndex = 0;
    let column = allColumns?.[0];
    let focusedField = allColumns?.[1]?.getColId();
    let ensureIndexVisiblePosition: "top" | "bottom" | "middle" | null = 'top';
   
    if (type === 'end') {
      column = allColumns?.[allColumns?.length - 1];
      rowIndex = (cachePageResultDataRef.current.length -1 ) || 0;
      rowNode = event?.api?.getRowNode(rowIndex.toString());
      ensureIndexVisiblePosition = null;
      focusedField = allColumns?.[allColumns?.length - 1]?.getColId();
    }

    if (rowNode && event?.api) {
      event.api.deselectAll();
      event.api.ensureIndexVisible(rowIndex, ensureIndexVisiblePosition);
      rowNode?.setSelected(true)
      event.api.ensureColumnVisible(column)
      event.api.setFocusedCell(rowIndex, focusedField);
    }
  }
  const moveToFirstOrLastColumn = (event: CellKeyDownEvent, type: 'left' | 'right') => {
    const allColumns = event?.columnApi?.getAllDisplayedColumns();

    // 过滤掉序号列，找到真正的数据列
    const dataColumns = allColumns?.filter(col => {
      const colId = col.getColId();
      const colDef = col.getColDef();
      // 排除序号列和其他非数据列
      return colId !== 'rowNumber' && colDef.cellClass !== 'number-cell' && colId !== indexColId;
    });

    let column = dataColumns?.[0];
    if (type === 'right') {
      column = dataColumns?.[dataColumns?.length - 1];
    }

    if (column && event?.api && customSelectionControllerRef.current) {
      // 获取当前焦点单元格的行索引
      const currentFocusedCell = event.api.getFocusedCell();
      const currentRowIndex = currentFocusedCell?.rowIndex || 0;

      // 使用自定义选择控制器清除所有选择
      customSelectionControllerRef.current.clearAllSelections();

      // 确保列可见
      event.api.ensureColumnVisible(column);
      if (customSelectionControllerRef.current) {
        // 使用自定义选择控制器选中目标单元格
        customSelectionControllerRef.current.selectCell(currentRowIndex, column.getColId());
        // 更新选择上下文
        updateSelectionContext();
      }
    }
  }
  const moveToLastRowOfNextPage = useCallback((event: any, type: 'next' | 'up') => {
    if (!gridApiRef.current) return
    const gridApi = gridApiRef.current?.api

    let currentRowIndex = gridApi.getFocusedCell()?.rowIndex || 0;

    let nextPageLastRowIndex = currentRowIndex + visibleRowCount - 1;
   
    if (type === 'up') {
      if (currentRowIndex < visibleRowCount) {
        nextPageLastRowIndex = 0;
      }else {
        nextPageLastRowIndex = currentRowIndex - visibleRowCount + 1;
      }
    }

    if (currentRowIndex !== undefined) {

      const rowNode = event?.api?.getRowNode(`${nextPageLastRowIndex}`)

      const ensureRowIndex = rowNode?.rowIndex || 0
      event?.api?.deselectAll()
      setTimeout(() => {
        if (type === 'up') {
          event.api.ensureIndexVisible(ensureRowIndex, 'top')
        } else {
          event.api.ensureIndexVisible(ensureRowIndex, 'bottom')
        }
      }, 100)
      if (rowNode) {
        event?.api?.selectNode(rowNode, true)
        gridApi.setFocusedCell(nextPageLastRowIndex, event.column);
      }
    }
  }, [visibleRowCount]);

  const downloadFocusedCell = async (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => {
    const modifyParams: ResultModify = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: [
        { resultOldData: oldData, resultNewData: newData , resultOperating: 'RENDER'},
      ],
    }
    const statements = await formatAndModifyResult(modifyParams)
    const executeParams = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statements: statements || [],
      tabKey: queryKey,
    }
    return downloadGridCell(executeParams)
  }

  const fetchFocusedCell = async (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => {
    const modifyParams: ResultModify = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      columnInfos: ColumnInfosEdit,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      resultOperationDates: [
        { resultOldData: oldData, resultNewData: newData, resultOperating: 'RENDER'},
      ],
    }
    const statements = await formatAndModifyResult(modifyParams)
    const executeParams = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statements: statements || [],
      tabKey: queryKey,
    }
    return getCompleteCell(executeParams)
  }

  const isDataExportPermissionEnabled = Service.moduleService.isModuleExist(
    '/flow',
  )
    ? dataExport.status
    : false

  /* 打开申请流程权限表单 */
  const applyDataExportPermission = async (exportType?:string) => {
    const elements = permissionResult?.allNodePathString?.map((item:any)=>({
      nodePath:item,
      connectionType: dataSourceType
    }))

    dispatch(
      openFlowForm({
        type: 'dataExport',
        fields: {
          elements: elements,
          operationList: exportType ? [exportType] : undefined,
        },
      }),
    )
  }

  const onOpenExportModal = (key: string) => {
    const visibleMap = {
      ResultExport: () => setVisibleCommonExport(true),
      SelectedResultExport: () => setVisibleSelectedExport(true),
      ResultAllExport: () => setVisibleExportAll(true),
    }
    if (key in visibleMap) {
      visibleMap[key as keyof typeof visibleMap]()
    }
  }

  const handleExportAllResult = useCallback(
    (data: any) => {
      const params = {
        connectionId: result.connectionId,
        connectionType: result.dataSourceType,
        databaseName: result.databaseName,
        operatingObject: result.operatingObject,
        statement: result.statement,
        containTempTable: result.containTempTable,
        tabKey: result.tabKey,
      }
      return makeResultAllExport(Object.assign({}, params, data))
      .then(() => {
        exportTaskCreatedNot()
      })
    },
    [result],
  )

  const handleViewCell = useCallback(() => {
    setVisibleCellViewer(true)
  }, [])

  useEffect(() => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    api.ensureIndexVisible(focusedRowIndex || 0)
  }, [focusedRowIndex, rowNum])

  const handleViewRow = useCallback(() => {
    setVisibleRowViewer(true)
  }, [])

  const agContext = useMemo<IGridContext>(
    () => {
      return {
        canSave,
        status,
        disableContextMenu: paneType === 'tSql',
        copyable: canCopy,
        canCopyCell,
        allowClone: !(connectionType === 'DB2' || connectionType === 'StarRocks' || connectionType === 'Inceptor') && editable,
        allowCreateSql: permissionResult?.supportResultSetSqlGenerator,
        createSqlDisabled: permissionResult?.resultSetSqlGenerator && enableGenerateSql,
        handleCopyCell,
        handleCopyRow,
        handleCopyAll: (context: ICellRendererParams) => {
          handleCopyAll(context, cachePageResultDataRef.current)
        },
        handleCopyCellTitle,
        handleCopyAllTitle,
        handleCopyWithTitle,
        handlePasteRow,
        handleViewCell,
        handleViewRow,
        handleCloneRow,
        handleResInsert,
        handleResUpdate,
        handleResDelete,
        // 全选逻辑 - 包装函数以匹配接口签名
        handleCopySelectAllWithTitle: (context: ICellRendererParams) => {
          handleCopySelectAllWithTitle(context, cachePageResultDataRef.current)
        },
        // 新增的动态菜单处理函数
        handleCopySelectedColumnTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnTitles : undefined,
        handleCopySelectedColumnData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnData : undefined,
        handleCopySelectedCellsData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsData : undefined,
        handleCopySelectedCellsTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsTitles : undefined,
        handleCopySelectedAllCellsTitles,
        handleCopySelectedCellsWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsWithTitle : undefined,
        handleCopySelectedFullColumnDataWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedFullColumnDataWithTitle : undefined,
        getSelectedRowsDataForResultSet: contextHandlersReady ? contextMenuHandlersRef.current?.getSelectedRowsDataForResultSet : undefined,
        // 新增：提供获取选择上下文的方法
        getSelectionContext: () => {
          return customSelectionControllerRef.current?.getCurrentSelectionContext() || null;
        },
        // 新增：选中单元格的方法
        selectCell: (params: ICellRendererParams) => {
          if (customSelectionControllerRef.current && params.column && params.rowIndex != null) {
            const colId = params.column.getColId();
            customSelectionControllerRef.current.selectCell(params.rowIndex, colId);
            updateSelectionContext();
          }
        },
        // 新增：直接的选择上下文属性
        selectionContext: selectionContext,
        // 添加 queryKey 到 context 中，供 SimpleTextRenderer 使用
        queryKey: queryKey,
      }
    },
    [
      canCopyCell,
      connectionType,
      handleViewCell,
      handleViewRow,
      handleCloneRow,
      handleResInsert,
      handleResUpdate,
      handleResDelete,
      handlePasteRow,
      paneType,
      status,
      permissionResult?.resultSetSqlGenerator,
      canSave,
      permissionResult?.supportResultSetSqlGenerator,
      enableGenerateSql,
      editable,
      canCopy,
      contextHandlersReady,
      selectionContext,
      handleCopySelectAllWithTitle,
      handleCopySelectedAllCellsTitles,
      updateSelectionContext,
      queryKey,
    ],
  )

  const executePayload = useMemo<QueryBase>(
    () => ({
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statements: [statement],
      tabKey: queryKey,
    }),
    [connectionId, dataSourceType, databaseName, operatingObject, queryKey, statement]
  )

  // 计算分页数据
  const paginationData = useMemo(() => {
    const currentPage = curPagination?.pageNumber || 1;
    const pageSize = curPagination?.pageSize || rowNum;
    const startRow = (currentPage - 1) * pageSize + 1;
    const endRow = startRow + (cachePageResultDataRef.current?.length || 0) - 1;
    
    // 优化 hasNextPage 的判断逻辑：优先使用已知的总数计算
    let calculatedHasNextPage = hasNextPage;
    if (curPagination?.total !== null && curPagination?.total !== undefined && curPagination?.total > 0) {
      const totalPages = Math.ceil(curPagination.total / pageSize);
      calculatedHasNextPage = currentPage < totalPages;
    } else {
      // 当没有总数时，使用 N+1 逻辑：如果当前页数据量等于页面大小，则可能有下一页
      // 除非明确知道没有下一页（通过 hasNextPage 状态）
      const currentPageDataLength = cachePageResultDataRef.current?.length || 0;
      if (currentPageDataLength === pageSize) {
        calculatedHasNextPage = hasNextPage; // 依赖从接口返回的 hasNextPage 状态
      } else {
        calculatedHasNextPage = false; // 当前页数据不足页面大小，肯定没有下一页
      }
    }
    
    return {
      currentPageNumber: currentPage,
      pageSize,
      startRowInPage: startRow,
      endRowInPage: Math.max(startRow, endRow),
      hasNextPage: calculatedHasNextPage,
      isLoading: loading || pending,
      total: curPagination?.total // 传递总数给分页组件
    };
  }, [curPagination?.pageNumber, curPagination?.pageSize, curPagination?.total, rowNum, hasNextPage, loading, pending, cachePageResultDataRef.current?.length]);

  // 处理页码变化
  const handlePageChange = useCallback((newPageNumber: number) => {
    setFocusedColumn(null);
    setFocusedRowIndex(null);
    setFocusedRowData({})

    // 检查是否有单元格正在编辑
    const editingCells = gridApiRef.current?.api?.getEditingCells();
    if (editingCells && editingCells.length > 0) {
      return message.warn(t('sdo_complete_current_edit'))
    } else if (editable && enabledActions.includes('save')) {
      return message.warn(t('sdo_save_current_page'))
    }

    // 保存当前的过滤和排序状态
    let currentFilterModel = null;
    let currentSortModel = null;
    if (gridApiRef.current?.api) {
      currentFilterModel = gridApiRef.current.api.getFilterModel();
      currentSortModel = gridApiRef.current?.columnApi?.getColumnState()?.filter((col: any) => col.sort)?.map((col: any) => ({ colId: col.colId, sort: col.sort })) || [];
    }

    dispatchAction('reset')

    // 更新分页状态，保持已知的总数
    setCurPagination({
      ...curPagination,
      pageNumber: newPageNumber,
      // 保持现有的 total 值，不要重置
    })

    // 如果已知总数，立即更新 hasNextPage 状态
    if (curPagination?.total !== null && curPagination?.total !== undefined && curPagination?.total > 0) {
      const pageSize = curPagination?.pageSize || rowNum;
      const totalPages = Math.ceil(curPagination.total / pageSize);
      setHasNextPage(newPageNumber < totalPages);
    }

    // 更新组件状态中的过滤和排序模型，确保在分页切换时保持
    if (currentFilterModel && Object.keys(currentFilterModel).length > 0) {
      setFilterModel(currentFilterModel);
    }
    if (currentSortModel && currentSortModel.length > 0) {
      setSortModel(currentSortModel);
    }

    // 分页后重新触发搜索
    retriggerSearchAfterPagination()
  }, [curPagination, dispatchAction, t, editable, enabledActions, retriggerSearchAfterPagination, rowNum]);

  // 处理页容量变化
  const handlePageSizeChange = useCallback((newPageSize: number) => {
    // 保持已知的总数，即使改变页面大小
    const currentTotal = curPagination?.total;
    setCurPagination({
      ...curPagination,
      pageSize: newPageSize,
      pageNumber: 1, // 重置到第一页
      total: currentTotal, // 保持总数
    })
    handleRefresh(true); // 保持过滤条件
    retriggerSearchAfterPagination()
  }, [curPagination, handleRefresh, retriggerSearchAfterPagination]);

  const handleToolbarView = useCallback(() => {
    if (!focusedColumn) return
    const colId = focusedColumn.getColId()
    if (colId === indexColId) {
      // 是序号列，展示行数据
      setVisibleRowViewer(true)
      return
    }
    // 展示单元格数据
    setVisibleCellViewer(true)
  }, [focusedColumn])

  useEffect(() => {
    if (copySetting) return
    if (visibleRowViewer || visibleCellViewer) {
      document.oncopy = (e) => {
        e.returnValue = false
      }
      document.oncontextmenu = (e) => {
        e.returnValue = false
      }
    } else {
      document.oncopy = (e) => {
        e.returnValue = true
      }
      document.oncontextmenu = (e) => {
        e.returnValue = true
      }
    }
  }, [visibleRowViewer, visibleCellViewer, copySetting])

  const lastRowIndex = () => {
    setFocusedRowIndex((index: number | null) => {
      return index? index - 1 : index
    })
  }

  const nextRowIndex = () => {
    setFocusedRowIndex((index: number | null) => {
      return (index || index === 0)? index + 1 : index
    })
  }

  const handleRowDataWithEditable = (data: any[]) => {
    return (
      data.map((curItem: any) => {
        const customCurItem = Object.assign({ editable: true }, curItem);
        const keys = Object.keys(curItem);
    
        for (const key of keys) {
          const value = curItem[key];

          // 如果包含 cursorValue，保留整个对象；否则只保留 value 字段
          if (value?.cursorValue !== undefined) {
            customCurItem[key] = value; // 保留完整对象以便访问 cursorValue
          } else {
            customCurItem[key] = value?.value;
          }

          if (value?.formatValue) {
            customCurItem[`get${key}`] = () => value.formatValue;
          }

          if (value?.renderType?.includes('binary') && value?.size) {
            customCurItem[`${key}CQSize`] = value.size;
          }

          if (!value?.editable) {
            customCurItem.editable = false;
          }
        }

        return customCurItem;
      })
    )
  }

  //通用getRows
  const commonGetRows = async(params: IGetRowsParams & {page?: number }, isPaginationAction: boolean = false) => {
      // 在数据量大或其他情况导致获取结果慢的情况下，不允许有其他获取结果集的操作
      const {
        startRow,
        endRow,
        sortModel: sortInfo,
        filterModel,
        successCallback,
        failCallback,
        context,
        page = 1
      } = params
 
      console.log('getRows')
      const { status } = context as IGridContext
      //每次分页都需要更新
      curCallbackRef.current  = successCallback
    
      // 编辑后没保存数据时直接return (当startRow % rowNum === 0 时，是分页的接口请求会触发getRows)
      if (canSave || ['CLONE', 'INSERT', 'UPDATE'].includes(status)) {
        // 空列表插入数据时会触发getRows，此时status为INSERT，startRow为0，直接return
        if(status === 'INSERT' && cachePageResultDataRef?.current?.length === 1) {
          return
        }
        failCallback()
        return message.error(t('sdo_save_first'))
      }

      setLoading(true)

      try {
        // 不支持分页不需要重新请求数据
        if(notSupportPageResultDataRef.current?.length){

          const resultData = notSupportPageResultDataRef.current                                                                                
          const curResultData = resultData.slice(startRow, endRow)  
          cachePageResultDataRef.current = curResultData

          let totalRows = resultData.length
          setPageTotal(totalRows)
          successCallback(curResultData, curResultData.length)
          return
        }else if (isDesensitizedPlaintext && startRow === 0) {
          let totalRows = detailedResultData?.length
          setPageTotal(totalRows)
          successCallback(currentRusultData, getLastRow(currentRusultData, startRow))
          return
        }

        /* 0 格式化过滤参数 */
        const formattedFilterModel = formatFilterModel(filterModel);
        /* 0 处理排序数据 应后端要求增加一个index列标值*/
        let sortModel:any[] = sortInfo
        if(!isEmpty(sortInfo)) {
          sortModel = sortInfo.map((item: any) => {
            return {
              ...item,
              index: columnDefs.findIndex((col) => col.field === item.colId)
            }
          })
        }
        /* 1 获取块数据 */
        let response = await fetchBlockDataOrFromStore(
          startRow,
          sortModel,
          formattedFilterModel
        )
        let [{ resultData: defaultResultData,detailedResultData: defaultDetailResultData, executeError, supportPage,
         databaseName,connectionId,dataSourceType,operatingObject,statementObject:tableName
        }] = response || []
        /* 2 错误处理 */
        if (executeError) {
          // throw Error(executeError.message)
          console.error(executeError.message)
        }
        setRowViewerResultData((data: any[]) => {
          return [...data, ...defaultResultData]
        })
        // 增加editable字段，用于判断是否可以删除
        let resultData = handleRowDataWithEditable(defaultDetailResultData);
        //oracle自定义字段
        if (dataSourceType === 'Oracle') {
          resultData = getExpandedCurResultData(defaultDetailResultData);
        } 

        // N+1分页逻辑：判断是否有下一页
        let actualResultData = resultData;
        let hasNext = false;
        let calculatedTotal: number | null = null; // 默认不设置总数
        
        if (resultData?.length === (curPagination?.pageSize || rowNum) + 1) {
          // 返回了 pageSize + 1 条数据，说明有下一页
          hasNext = true;
          actualResultData = resultData.slice(0, curPagination?.pageSize || rowNum); // 只显示前 pageSize 条
          // 有下一页时，不设置总数，让分页组件继续使用 N+1 逻辑
          calculatedTotal = null;
        } else {
          // 返回的数据 <= pageSize，说明这是最后一页
          hasNext = false;
          actualResultData = resultData;
          // 当到达最后一页时，计算并设置准确的总数
          calculatedTotal = actualResultData.length + startRow;
        }
        
        setHasNextPage(hasNext);
        setPageTotal(actualResultData.length + startRow);

        // //缓存使用（缓存实际显示的数据）
        // // cacheAllPageResultDataRef.current[page] = actualResultData;
        cachePageResultDataRef.current = actualResultData;
        //返回实际显示的数据
        successCallback(actualResultData, actualResultData?.length);
        
        // 更新分页状态，确保总数被正确设置
        setCurPagination(prev => ({
          ...prev,
          pageNumber: page,
          total: calculatedTotal, // 直接使用 calculatedTotal，有下一页时为 null，最后一页时为准确总数
        }));
       
      } catch (error) {
        failCallback()
        console.log(error)
      } finally {
        setLoading(false)
        dispatchAction('reset')
      }
  }

  const loadPage = (page: number, pageSize?: number, sortModel?: any, filterModel?: any) => {
    const curPageSize = pageSize || curPagination?.pageSize || rowNum;
    const startRow = (page - 1) * curPageSize;
    const endRow = startRow + curPageSize;

    gridApiRef?.current?.api?.setDatasource({
      getRows: async (params) => {
        const finalParams = {
          ...params,
          startRow,
          endRow,
          page,
          // 优先使用保存的过滤和排序状态，如果没有则使用 AG-Grid 的默认值
          sortModel: sortModel || params.sortModel || [],
          filterModel: filterModel || params.filterModel || {}
        };
        commonGetRows(finalParams, true);
      }
    });
  };

  useEffect(() => {
    console.log('loadPage')
    loadPage(curPagination?.pageNumber,curPagination?.pageSize,sortModel, filterModel)
  },[curPagination?.pageNumber,curPagination?.pageSize,JSON.stringify(sortModel),JSON.stringify(filterModel)])

  const isCellEditing = () => {
    if (!gridApiRef.current) return false;

    const editingCells = gridApiRef.current?.api?.getEditingCells();

    return editingCells.length > 0;
  };
  const handleSelectionChanged = (event: any) => {
    const nodes = event?.api?.getSelectedNodes()
    setSelectedNodes(nodes)
  }

   //敏感资源申请
   const onApplyDensens = () => {
    if (maskedPathMap && Object.keys(maskedPathMap)?.length > 0) {
      //无权限的脱敏资源
      const noPermissionSensitiveResourceElements = Object.keys(maskedPathMap).filter(key => !maskedPathMap[key]).map(key => ({
        label: '',
        value: key
      }));

      dispatch(
        openFlowForm({
          type: 'desensitizedResource',
          fields: {
            //@ts-ignore
            elements: noPermissionSensitiveResourceElements,
            //@ts-ignore
            connectionId,
            connectionType,
            nodeType: '',
          },
        })
      );
    }
  }

  // 排序变化时触发的回调
  const onSortChanged = () => {
    setRowViewerResultData([])
    const sortModel = gridApiRef?.current?.columnApi?.getColumnState()?.filter((col: any) => col.sort)?.map((col: any) => ({ colId: col.colId, sort: col.sort })) || [];
     //排序发生变化 重置缓存数据
     // cacheAllPageResultDataRef.current = {};
     setSortModel(sortModel)

    // 清除双击单元格气泡状态
     dispatch(setDoubleClickCell({}));
  };

  //过滤条件变化时触发的回调
  const onFilterChanged  =() => {
    setRowViewerResultData([])
    const filterModel = gridApiRef?.current?.api?.getFilterModel();
     //排序发生变化 重置缓存数据
     // cacheAllPageResultDataRef.current = {};
     setFilterModel(filterModel);
     setFilterSql(null); // 重置 filterSql

     // 过滤条件变化时，重置所有分页相关状态
     setHasNextPage(false); // 重置下一页状态，让 N+1 逻辑重新判断
     setCurPagination({
       ...curPagination,
       pageNumber: 1,
       total: null, // 重置总数，让 N+1 逻辑重新计算
       maxNumber: null // 重置最大数量
     })

     // 重置缓存的分页数据
     cachePageResultDataRef.current = [];

     // 清除双击单元格气泡状态
     dispatch(setDoubleClickCell({}));
  }
  
  // 首次数据渲染完成（含异步数据）回调--自适应调整列宽
  const onFirstDataRendered = useCallback((params: any) => {
    const { columnApi, api } = params;

    const adjustColumns = () => {
      // 仅在获取第一页数据时（包括刷新），进行列宽自适应调整
      const colIds = columnApi.getAllColumns()
        ?.filter((col: any) => col.getColId() !== indexColId)
        ?.map((col: any) => col.getColId());

      if (colIds?.length && api.getDisplayedRowCount() > 0) {
        // 能够获取到表头节点的列做自适应调整
        let autoSizeIds: any[] = []
        colIds.forEach((id: number | string) => {
          const headerElement = document.querySelector(`[col-id="${id}"] .${CustomHeaderStyles.headerWrapper}`);
          // 如果表头节点存在，说明已经进行了dom渲染，可以进行列宽自适应调整
          if (headerElement) {
            autoSizeIds.push(id)
          }
        });

        // 先执行自动调整
        columnApi.autoSizeColumns(autoSizeIds, false);
        // 然后遍历所有列，限制最大宽度为300px
        autoSizeIds.forEach(id => {
          const column = columnApi.getColumn(id);
          const currentWidth = column.getActualWidth();
          if (currentWidth > 300) {
            columnApi.setColumnWidth(id, 300);
          }
        });

        // 移除 modelUpdated 监听器
        if (modelUpdatedHandlerRef.current) {
          api.removeEventListener('modelUpdated', modelUpdatedHandlerRef.current);
        }
      }
    }

    // 定义 modelUpdated 事件处理函数
    const handleModelUpdated = () => {
      // 数据更新后，延迟调整列宽，autoSizeColumns的调整基于dom节点的宽度计算
      // 故使用 setTimeout 可以将列宽调整的操作推迟到下一个事件循环，确保 DOM 已经完全渲染。
      setTimeout(adjustColumns, 100);
    };

    // 保存监听器引用以便后续移除
    modelUpdatedHandlerRef.current = handleModelUpdated;

    // 监听数据更新事件，重新调整列宽
    api.addEventListener('modelUpdated', handleModelUpdated);
  }, []);


  // 拖拽选区功能事件监听器
  useEffect(() => {
    return setupDragSelection(gridApiRef);
  }, [setupDragSelection]);

  // 清理自定义选择控制器
  useEffect(() => {
    return cleanupSelectionController;
  }, [cleanupSelectionController]);

  const handleSearchAg = async(searchText: string, options: any)=>{
   await dispatchAction('reset')
   handleSearch(searchText, options, )
  }

  return (
    <div className={styles.resultGrid}>
      <div className={styles.resultContent} ref={copyableRef}>
        {
            !columnInfos || columnInfos.length === 0
              ?
              <div className={styles.resultGrid__filter}>
                <Iconfont type='icon-yizhongzhi' style={{ marginRight: '4px' }} />
                {t('sdo_result_empty_filtered_field')}：{filterNames?.join(',')}
              </div>
              :
            <Spin spinning={loading || pending} wrapperClassName={styles.gridSpinContainer}>
                <ResultToolbar
                  enabledActions={enabledActions}
                  paneType={paneType}
                  onRefresh={handleRefresh}
                  onAddRow={handleAddRow}
                  onDeleteRow={handleDeleteRowConfirm}
                  onConfirmModify={handleConfirmModify}
                  onScalePage={handleScaleView}
                  onCancelModify={handleCancelModify}
                  onViewCell={handleToolbarView}
                  readOnly={!editable}
                  refreshable={refreshable}
                  isDataExportPermissionEnabled={isDataExportPermissionEnabled}
                  applyDataExportPermission={applyDataExportPermission}
                  onOpenExportModal={onOpenExportModal}
                  showExported={dataExport.showExported}
                  connectionType={connectionType}
                  executePayload={executePayload}
                  scale={scale === undefined ? 100 : scale}
                  type={type}
                  permissionResult={permissionResult}
                  dataExport={dataExport}
                  filterNames={filterNames}
                  txMode={txMode}
                  onOpenFieldsModal={() => { setVisibleDesensitizedFields(true) }}
                  isDesensitized={isDesensitized}
                  onSave={handleSave}
                  //敏感资源申请
                  onApplyDensens={onApplyDensens}
                  maskedPathMap={maskedPathMap}
                  // 搜索功能相关 props
                  onSearch={handleSearch}
                  setVisibleSearchBox={setSearchVisible}
                  isSearchActive={searchVisible}
                  // N+1 分页相关属性
                  showPagination={true}
                  currentPageNumber={paginationData.currentPageNumber}
                  pageSize={paginationData.pageSize}
                  startRowInPage={paginationData.startRowInPage}
                  endRowInPage={paginationData.endRowInPage}
                  hasNextPage={paginationData.hasNextPage}
                  isLoading={paginationData.isLoading}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  totalFromParent={paginationData.total}
                  settingPageSize={tabInfoMap[activeTabKey]?.settingPageSize}
                  filterSql={filterSql}
                />
                  {searchVisible && (
                    <ResultSearchBar
                      visible={searchVisible}
                      totalMatches={totalMatches}
                      currentMatch={currentMatchIndex + 1}
                      onSearch={handleSearchAg}
                      onNavigate={handleSearchNavigation}
                      onQuickFilter={handleQuickFilter}
                      setNeedFullUpdate={setNeedFullUpdate}
                      onClose={() => {
                        setSearchVisible(false)
                        clearAllHighlights()
                      }}
                      needFullUpdateRef={needFullUpdateRef}
                    />
                )}
                <div
                  id={`${tabResultKey}gridWrapper`}
                  ref={gridContainerRef}
                  className={classNames(
                    styles.gridWrapper,
                    theme === 'dark' ? 'ag-theme-balham-dark' : 'ag-theme-balham',
                    !canCopy && styles.unCopyable,
                  )}
                >
                  <AgGridReact
                    {...gridConfig}
                    {...infiniteModeOptions}
                    columnDefs={columnDefs}
                    cacheBlockSize={rowNum}
                    defaultColDef={defaultColDef}
                    datasource={datasource}
                    onGridReady={handleGridReady}
                    onCellFocused={handleCellFocused}
                    onCellKeyDown={onCellKeyDown} // 单元格按键事件
                    onCellClicked={(params) => {
                      // 检查是否有单元格正在编辑
                      const editingCells = params.api?.getEditingCells();
                      const isEditing = editingCells && editingCells.length > 0;

                      // 如果正在编辑模式，不调用自定义处理器
                      if (!isEditing) {
                        handleCellClicked(params);
                      }
                    }} // 自定义单元格点击处理
                    onCellContextMenu={(params) => {
                      // 处理右键菜单显示前的选区逻辑
                      if (contextMenuHandlersRef.current) {
                        contextMenuHandlersRef.current.handleBeforeContextMenu(params);
                      }
                    }}
                    onRowEditingStarted={handleStartRowEditing}
                    onRowEditingStopped={handleStopRowEditing}
                    context={agContext}
                    suppressDragLeaveHidesColumns={true}
                    // 添加 key 属性，需要刷新的地方用递增的数值setAggridKey
                    key={aggridKey}
                    onSelectionChanged={handleSelectionChanged}
                    onSortChanged={onSortChanged}
                    pagination={true}
                    paginationPageSize={curPagination?.pageSize || rowNum}
                    suppressPaginationPanel={true}
                    onFilterChanged={onFilterChanged}
                    // 首次数据渲染完成回调
                    // onFirstDataRendered={onFirstDataRendered}
                  />
                </div>
            </Spin>
}
      </div>
      <CellViewerModal
        gridApi={gridApiRef.current?.api || null}
        rowIndex={focusedRowIndex}
        rowData={focusedRowData}
        initRowData={isEmpty(focusedRowData?._initData) ? focusedRowData : focusedRowData?._initData }
        resultData={detailedResultData}
        //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
        editable={editable && connectionType !== 'DB2'}
        allowResultCopy={permissionResult?.resultCellCopy}
        visible={visibleCellViewer}
        allowBinaryCellDownload={permissionResult?.resultSetBinaryFileDownload}
        setVisible={setVisibleCellViewer}
        updateFocusedCell={updateFocusedCell}
        downloadFocusedCell={downloadFocusedCell}
        fetchFocusedCell={fetchFocusedCell}
        type={type!}
        isExplain={isExplain}
      />
      {visibleRowViewer && (() => {
        // 计算绝对索引：第二页开始每次减一修正偏移
        const pageNumber = curPagination?.pageNumber || 1;
        const pageSize = curPagination?.pageSize || rowNum;
        const offset = pageNumber > 1 ? pageNumber - 1 : 0;
        const absoluteRowIndex = (pageNumber - 1) * pageSize + (focusedRowIndex || 0) + offset;
        const currentRowData = rowViewerResultData[absoluteRowIndex] || {};
        const nextAbsoluteIndex = absoluteRowIndex + 1;
        const isLastRow = nextAbsoluteIndex >= rowViewerResultData.length;

        return (
          <RowViewerModal
            gridApi={gridApiRef.current?.api || null}
            rowData={currentRowData}
            resultData={detailedResultData}
            //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
            editable={editable && connectionType !== 'DB2'}
            visible={visibleRowViewer}
            setVisible={setVisibleRowViewer}
            updateFocusedCell={updateFocusedCell}
            downloadFocusedCell={downloadFocusedCell}
            fetchFocusedCell={fetchFocusedCell}
            rowIndex={focusedRowIndex}
            nextRowIndex={nextRowIndex}
            lastRowIndex={lastRowIndex}
            isLastRowIndex={isLastRow}
            setRowViewerResultData={() => {
              setRowViewerResultData([])
            }}
            resultAllowCopy={canCopyCell}
          />
        );
      })()}
      <AddSelectedResultExportModal
        result={result}
        visible={visibleSelectedExport}
        setVisible={setVisibleSelectedExport}
        gridApi={gridApiRef.current?.api || null}
        permissionResult={permissionResult}
      />
      <AddResultExportModal
        result={result}
        visible={visibleCommonExport}
        setVisible={setVisibleCommonExport}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />
      <ResultAllExport
        result={result}
        visible={visibleExportAll}
        setVisible={setVisibleExportAll}
        hanldeExportAll={handleExportAllResult}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />
      <DesensitizedFieldsModal
        result={result}
        visible={visibleDesensitizedFields}
        setVisible={setVisibleDesensitizedFields}
        doubleCheckType={doubleCheckType}
        filterNames={filterNames}
      />
    </div>
  )
})